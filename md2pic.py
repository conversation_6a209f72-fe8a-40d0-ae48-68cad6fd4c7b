import tempfile
from typing import Optional
import markdown
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright, <PERSON>rowser
from playwright.async_api import async_playwright, <PERSON>rowser as AsyncBrowser
import atexit
import threading
import asyncio
from nonebot import logger


class BrowserManager:
    """单例模式的浏览器管理器，用于复用浏览器实例"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.playwright = None
            self.browser = None
            self._initialized = True
            # 注册退出时的清理函数
            atexit.register(self.cleanup)
    
    def initialize(self):
        """初始化浏览器实例"""
        if self.browser is None:
            try:
                logger.info("正在初始化浏览器...")
                self.playwright = sync_playwright().start()
                self.browser = self.playwright.chromium.launch(headless=True)
                logger.info("浏览器初始化完成")
            except Exception as e:
                logger.error(f"浏览器初始化失败: {e}")
                raise
    
    def get_browser(self) -> Browser:
        """获取浏览器实例"""
        if self.browser is None:
            self.initialize()
        return self.browser
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                logger.info("正在关闭浏览器...")
                self.browser.close()
                self.browser = None
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"清理浏览器资源时出错: {e}")
    
    def restart(self):
        """重启浏览器（用于错误恢复）"""
        logger.info("正在重启浏览器...")
        self.cleanup()
        self.initialize()


class AsyncBrowserManager:
    """异步版本的浏览器管理器"""
    
    _instance = None
    _lock = None  # 将在 __new__ 中初始化
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.playwright = None
            self.browser = None
            self._initialized = True
            # 初始化异步锁
            if AsyncBrowserManager._lock is None:
                AsyncBrowserManager._lock = asyncio.Lock()
    
    async def initialize(self):
        """异步初始化浏览器实例"""
        if self.browser is None:
            # 使用类级别的锁，确保在不同事件循环中也能正确同步
            if AsyncBrowserManager._lock is None:
                AsyncBrowserManager._lock = asyncio.Lock()
            
            async with AsyncBrowserManager._lock:
                if self.browser is None:  # 双重检查
                    try:
                        logger.info("正在异步初始化浏览器...")
                        self.playwright = await async_playwright().start()
                        self.browser = await self.playwright.chromium.launch(headless=True)
                        logger.info("异步浏览器初始化完成")
                    except Exception as e:
                        logger.error(f"异步浏览器初始化失败: {e}")
                        raise
    
    async def get_browser(self) -> AsyncBrowser:
        """获取异步浏览器实例，如果未初始化则自动初始化"""
        if self.browser is None:
            await self.initialize()
        return self.browser
    
    async def cleanup(self):
        """异步清理资源"""
        try:
            if self.browser:
                logger.info("正在关闭异步浏览器...")
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
                logger.info("异步浏览器已关闭")
        except Exception as e:
            logger.error(f"清理异步浏览器资源时出错: {e}")
    
    async def restart(self):
        """异步重启浏览器"""
        logger.info("正在重启异步浏览器...")
        await self.cleanup()
        await self.initialize()


# 全局浏览器管理器实例
browser_manager = BrowserManager()
async_browser_manager = AsyncBrowserManager()


class MarkdownRenderer:
    """A class to render Markdown content to images using web technologies."""
    
    def __init__(self):
        self.html_template = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Render</title>
    
    <!-- Prism.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <!-- KaTeX for LaTeX rendering -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/contrib/auto-render.min.js"></script>
    
    <!-- KaTeX configuration -->
    <script>
        // 快速就绪检查
        window.katexReady = false;
        window.prismReady = false;
        window.readyCallbacks = [];
        
        function checkReadiness() {{
            const hasMath = document.body.innerHTML.includes('$') || 
                           document.body.innerHTML.includes('\\\\(') ||
                           document.body.innerHTML.includes('\\\\[');
            const hasCode = document.querySelector('pre code') !== null;
            
            const mathReady = !hasMath || window.katexReady;
            const codeReady = !hasCode || window.prismReady;
            
            if (mathReady && codeReady) {{
                document.body.setAttribute('data-ready', 'true');
                // 执行所有回调
                window.readyCallbacks.forEach(cb => cb());
                window.readyCallbacks = [];
            }}
        }}
        
        // KaTeX 渲染完成后的回调
        function onKatexReady() {{
            window.katexReady = true;
            checkReadiness();
        }}
    </script>
    
    <style>
        body {{
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.7;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px 10px;
            background-color: #fff;
            letter-spacing: 0.01em;
            font-size: 18px;
        }}
        
        #content {{
            width: fit-content;
            max-width: 100%;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            margin-top: 2em;
            margin-bottom: 0.8em;
            font-weight: 700;
            line-height: 1.3;
            letter-spacing: -0.02em;
        }}
        
        h1 {{ 
            font-size: 2.4em;
            border-bottom: 2px solid #eaecef; 
            padding-bottom: 0.5em;
            margin-bottom: 1.2em;
        }}
        h2 {{ 
            font-size: 1.8em;
            border-bottom: 1px solid #eaecef; 
            padding-bottom: 0.4em;
            margin-bottom: 1em;
        }}
        h3 {{ font-size: 1.5em; }}
        h4 {{ font-size: 1.25em; }}
        h5 {{ font-size: 1.1em; }}
        h6 {{ font-size: 1.05em; color: #6a737d; }}
        
        p, li {{
            margin-bottom: 1.4em;
            text-align: justify;
            text-justify: inter-word;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 1em;
        }}
        
        /* 行内代码样式 - 更有质感，优化标题中的显示 */
        code {{
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.12));
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 4px;
            font-size: 0.9em;
            padding: 0.15em 0.4em;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #3b82f6;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-all;
            vertical-align: baseline;
            line-height: 1;
            display: inline-block;
            margin: 0 0.1em;
        }}
        
        /* 标题中的行内代码特殊处理 */
        h1 code, h2 code, h3 code, h4 code, h5 code, h6 code {{
            font-size: 0.8em;
            padding: 0.1em 0.3em;
            vertical-align: middle;
            line-height: 1;
            margin: 0;
            border-radius: 3px;
            font-weight: 500;
            /* 确保不会撑开标题行高 */
            max-height: 1.2em;
            overflow: hidden;
        }}
        
        /* 针对不同级别标题的代码块微调 */
        h1 code {{
            font-size: 0.75em;
            padding: 0.08em 0.25em;
        }}
        
        h2 code {{
            font-size: 0.77em;
            padding: 0.09em 0.28em;
        }}
        
        h3 code {{
            font-size: 0.8em;
            padding: 0.1em 0.3em;
        }}
        
        h4 code, h5 code, h6 code {{
            font-size: 0.85em;
            padding: 0.12em 0.32em;
        }}
        
        /* 代码块容器样式 - 简化版本 */
        .code-block-container {{
            position: relative;
            margin: 1.5rem 0;
            max-width: 100%;
            overflow: hidden;
        }}
        
        .code-block-container .language-label {{
            position: absolute;
            top: 15px;
            right: 16px;
            padding: 6px 14px;
            font-size: 11px;
            color: #9ca3af;
            background: rgba(31, 41, 55, 0.9);
            border-radius: 8px;
            font-weight: 600;
            backdrop-filter: blur(8px);
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
        
        pre {{
            background: #1a1b26 !important;
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid rgba(55, 65, 81, 0.3);
            margin-bottom: 1em;
            position: relative;
            max-width: 100%;
            overflow-x: hidden;
        }}
        
        /* 只添加 macOS 三个点 */
        pre::before {{
            content: '';
            position: absolute;
            top: 18px;
            left: 16px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff5f56;
            box-shadow: 
                20px 0 0 #ffbd2e,
                40px 0 0 #27ca40;
            z-index: 2;
        }}
        
        pre code {{
            background: transparent !important;
            border: 0;
            padding: 20px 0px;
            padding-top: 50px;
            font-size: 14px !important;
            line-height: 1.7;
            color: #e5e7eb !important;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
            display: block;
            position: relative;
            z-index: 3;
            max-width: 100%;
            
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            word-break: break-all !important;
            
            overflow-x: hidden !important;
            overflow-y: visible;
        }}
        
        /* 特殊处理：对于特别长的标识符或URL，使用更智能的换行 */
        pre code .token.string,
        pre code .token.url,
        pre code .token.attr-value {{
            word-break: break-all !important;
            overflow-wrap: break-word !important;
        }}
        
        /* 保持注释的可读性 */
        pre code .token.comment {{
            word-break: break-word !important;
            overflow-wrap: break-word !important;
        }}
        
        /* 数字和操作符尽量不换行 */
        pre code .token.number,
        pre code .token.operator {{
            word-break: keep-all;
        }}
        
        /* Prism.js 主题覆盖 */
        .token.comment,
        .token.prolog,
        .token.doctype,
        .token.cdata {{
            color: #7c7c7c;
            font-style: italic;
        }}
        
        .token.punctuation {{
            color: #c5c8c6;
        }}
        
        .token.property,
        .token.tag,
        .token.boolean,
        .token.constant,
        .token.symbol,
        .token.deleted {{
            color: #96cbfe;
        }}
        
        .token.selector,
        .token.attr-name,
        .token.char,
        .token.builtin,
        .token.inserted {{
            color: #a8ff60;
        }}
        
        .token.operator,
        .token.entity,
        .language-css .token.string,
        .style .token.string {{
            color: #ededed;
        }}
        
        .token.atrule,
        .token.keyword {{
            color: #cfcb90;
        }}
        
        .token.function,
        .token.class-name {{
            color: #ffd700;
        }}
        
        .token.regex,
        .token.important,
        .token.variable {{
            color: #e9c062;
        }}
        
        /* 修复 HTML 代码高亮时 .token.tag 样式冲突 */
        .token.tag {{
            padding: 0 !important;
            margin: 0 !important;
            background: transparent !important;
            border-radius: 0 !important;
            word-break: break-all !important;
        }}
        
        blockquote {{
            border-left: 4px solid #dfe2e5;
            margin: 1.5em 0;
            padding: 1em 1.5em;
            color: #6a737d;
            background: linear-gradient(135deg, rgba(223, 226, 229, 0.05), rgba(223, 226, 229, 0.1));
            border-radius: 0 8px 8px 0;
            font-style: italic;
            position: relative;
            font-size: 1em;
        }}
        
        blockquote::before {{
            content: '"';
            font-size: 4em;
            color: rgba(223, 226, 229, 0.3);
            position: absolute;
            top: -10px;
            left: 10px;
            font-family: Georgia, serif;
        }}
        
        /* 基础列表样式 */
        ul, ol {{
            margin-bottom: 1.5em;
        }}
        
        li {{
            margin-bottom: 0.5em;
            line-height: 1.6;
        }}
        
        /* 无序列表样式 */
        ul {{
            list-style: none;
            padding-left: 1.5em;
        }}
        
        ul li {{
            position: relative;
            margin-bottom: 0.5em;
            line-height: 1.6;
        }}
        
        ul li::before {{
            content: '•';
            color: #9ca3af;
            font-size: 1.2em;
            font-weight: bold;
            position: absolute;
            margin-left: -1.2em;
            margin-top: -0.1em;
        }}
        
        /* 嵌套无序列表样式 */
        ul ul {{
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-left: 1.5em;
        }}
        
        ul ul li::before {{
            content: '◦';
            font-size: 1.1em;
            color: #6b7280;
        }}
        
        /* 三级嵌套无序列表 */
        ul ul ul li::before {{
            content: '▪';
            font-size: 1em;
            color: #9ca3af;
        }}
        
        /* 有序列表样式 */
        ol {{
            counter-reset: item;
            padding-left: 1.5em;
            list-style: none;
        }}
        
        ol li {{
            counter-increment: item;
            position: relative;
            margin-bottom: 0.5em;
            line-height: 1.6;
        }}
        
        ol li::before {{
            content: counter(item) '.';
            color: #9ca3af;
            font-weight: 700;
            font-size: 1.1em;
            position: absolute;
            margin-left: -1.5em;
            width: 1.2em;
            text-align: right;
        }}
        
        /* 嵌套有序列表 */
        ol ol {{
            counter-reset: subitem;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-left: 1.5em;
        }}
        
        ol ol li {{
            counter-increment: subitem;
        }}
        
        ol ol li::before {{
            content: counter(item) '.' counter(subitem) '.';
            font-size: 1em;
            color: #6b7280;
        }}
        
        /* 混合嵌套：有序列表中的无序列表 */
        ol ul {{
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-left: 1.5em;
        }}
        
        ol ul li {{
            counter-increment: none;
        }}
        
        ol ul li::before {{
            content: '•';
            color: #9ca3af;
            font-size: 1.2em;
            font-weight: bold;
            position: absolute;
            margin-left: -1.2em;
            margin-top: -0.1em;
            width: auto;
            text-align: left;
        }}
        
        /* 混合嵌套：无序列表中的有序列表 */
        ul ol {{
            counter-reset: item;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-left: 1.5em;
        }}
        
        ul ol li {{
            counter-increment: item;
        }}
        
        ul ol li::before {{
            content: counter(item) '.';
            color: #9ca3af;
            font-weight: 700;
            font-size: 1.1em;
            position: absolute;
            margin-left: -1.5em;
            width: 1.2em;
            text-align: right;
        }}
        
        table {{
            border-collapse: collapse;
            margin: 2em 0;
            width: 100%;
            max-width: 100%;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            table-layout: auto;
            font-size: 1em;
        }}
        
        th, td {{
            border: 1px solid #e5e7eb;
            padding: 12px 16px;
            text-align: left;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 200px;
        }}
        
        th {{
            background: linear-gradient(135deg, #f9fafb, #f3f4f6);
            font-weight: 700;
            color: #374151;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.05em;
        }}
        
        tr:nth-child(2n) {{
            background-color: rgba(249, 250, 251, 0.5);
        }}
        
        tr:hover {{
            background-color: rgba(59, 130, 246, 0.05);
            transition: background-color 0.2s ease;
        }}
        
        a {{
            color: #0366d6;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.2s ease;
            font-size: 1em;
        }}
        
        a:hover {{
            border-bottom-color: #0366d6;
            background: linear-gradient(135deg, rgba(3, 102, 214, 0.05), rgba(3, 102, 214, 0.1));
            padding: 2px 4px;
            border-radius: 4px;
            margin: -2px -4px;
        }}
        
        hr {{
            border: none;
            height: 2px;
            background: linear-gradient(90deg, transparent, #eaecef, transparent);
            margin: 3em 0;
        }}
        
        /* KaTeX styling - 增强质感 */
        .katex {{
            font-size: 1.1em;
        }}
        
        /* KaTeX 行内公式样式 */
        .katex-inline {{
            padding: 0.1em 0.2em;
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.05), rgba(139, 69, 19, 0.1));
            border-radius: 4px;
            border: 1px solid rgba(139, 69, 19, 0.15);
        }}
        
        /* KaTeX 块级公式样式 */
        .katex-display {{
            margin: 1.5em 0;
            overflow-x: auto;
        }}
        
        /* 强调文本样式增强 */
        strong {{
            font-weight: 700;
            color: #1f2937;
        }}
        
        em {{
            font-style: italic;
            color: #4b5563;
        }}
        
        /* 添加微妙的文本阴影提升质感 */
        h1, h2, h3 {{
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }}
        
        /* Print-friendly styles */
        @media print {{
            body {{
                margin: 0;
                padding: 10px;
            }}
            
            .code-block-container {{
                box-shadow: none;
                border: 1px solid #ddd;
            }}
        }}
        
        /* 长URL和代码在表格中的处理 */
        td code, th code {{
            word-break: break-all !important;
            white-space: pre-wrap !important;
        }}
    </style>
    

</head>
<body>
    <div id="content">
        {content}
    </div>
    
    <script>
        // 高效的初始化逻辑
        document.addEventListener('DOMContentLoaded', function() {{
            const hasCode = document.querySelector('pre code') !== null;
            const hasMath = document.body.innerHTML.includes('$') || 
                           document.body.innerHTML.includes('\\\\(') ||
                           document.body.innerHTML.includes('\\\\[');
            
            // 如果没有需要处理的内容，立即标记为就绪
            if (!hasCode && !hasMath) {{
                document.body.setAttribute('data-ready', 'true');
                return;
            }}
            
            // 处理代码高亮
            if (hasCode && window.Prism) {{
                try {{
                    Prism.highlightAll();
                    window.prismReady = true;
                }} catch (e) {{
                    window.prismReady = true;
                }}
            }} else {{
                window.prismReady = true;
            }}
            
            // 处理数学公式 - 使用 KaTeX
            if (hasMath && window.katex && window.renderMathInElement) {{
                try {{
                    renderMathInElement(document.body, {{
                        delimiters: [
                            {{left: '$$', right: '$$', display: true}},
                            {{left: '$', right: '$', display: false}},
                            {{left: '\\\\[', right: '\\\\]', display: true}},
                            {{left: '\\\\(', right: '\\\\)', display: false}}
                        ],
                        throwOnError: false
                    }});
                    onKatexReady();
                }} catch (e) {{
                    console.warn('KaTeX rendering failed:', e);
                    onKatexReady();
                }}
            }} else {{
                window.katexReady = true;
                checkReadiness();
            }}
            
            // 立即检查就绪状态
            checkReadiness();
        }});
        
        // 快速后备机制 - 减少到1.5秒
        window.addEventListener('load', function() {{
            setTimeout(function() {{
                if (!document.body.getAttribute('data-ready')) {{
                    document.body.setAttribute('data-ready', 'true');
                }}
            }}, 1500);
        }});
    </script>
</body>
</html>"""
    
    def optimize_code_wrapping(self, soup: BeautifulSoup, max_line_length: int = 80) -> None:
        """
        优化代码块的换行，处理特别长的行
        
        Args:
            soup: BeautifulSoup对象
            max_line_length: 单行最大字符数，超过则添加换行提示
        """
        for pre_tag in soup.find_all('pre'):
            code_tag = pre_tag.find('code')
            if code_tag and code_tag.string:
                lines = code_tag.string.split('\n')
                processed_lines = []
                
                for line in lines:
                    if len(line) > max_line_length:
                        # 对于特别长的行，在合适的位置添加换行提示
                        # 优先在操作符、逗号、分号后换行
                        import re
                        
                        # 在这些字符后添加可能的换行点
                        break_chars = [',', ';', '&&', '||', '+', '-', '*', '/', '=', '(', '{', '[']
                        
                        processed_line = line
                        for char in break_chars:
                            if char in processed_line:
                                processed_line = processed_line.replace(char, char + '\u200B')  # 零宽空格
                        
                        processed_lines.append(processed_line)
                    else:
                        processed_lines.append(line)
                
                code_tag.string = '\n'.join(processed_lines)

    def markdown_to_html(self, markdown_content: str) -> str:
        """Convert Markdown content to HTML."""
        # 预检查内容类型
        has_math = '$' in markdown_content or '\\(' in markdown_content or '\\[' in markdown_content
        has_code = '```' in markdown_content or '`' in markdown_content
        
        logger.info(f"内容分析 - 数学公式: {has_math}, 代码块: {has_code}")
        
        # Configure markdown extensions
        md = markdown.Markdown(
            extensions=[
                'pymdownx.arithmatex',
                'codehilite',
                'fenced_code', 
                'tables',
                'toc',
                'sane_lists'
            ],
            extension_configs={
                'pymdownx.arithmatex': {
                    'generic': True
                },
                'codehilite': {
                    'css_class': 'highlight',
                    'use_pygments': False  # We'll use Prism.js instead
                }
            }
        )
        
        html_content = md.convert(markdown_content)
        
        # Clean up the HTML and add code block containers
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 优化代码换行
        self.optimize_code_wrapping(soup)
        
        # Process code blocks to add language labels and containers
        for pre_tag in soup.find_all('pre'):
            code_tag = pre_tag.find('code')
            if code_tag:
                # Extract language from class attribute
                lang = 'plain'
                if 'class' in code_tag.attrs:
                    classes = code_tag.attrs['class']
                    for cls in classes:
                        if cls.startswith('language-'):
                            lang = cls.replace('language-', '')
                            break
                        elif cls.startswith('highlight-'):
                            lang = cls.replace('highlight-', '')
                            break
                
                # Create container div
                container = soup.new_tag('div', **{'class': 'code-block-container'})
                
                # Create language label
                lang_label = soup.new_tag('div', **{'class': 'language-label'})
                display_lang = lang if lang != 'text' else 'plain'
                lang_label.string = display_lang
                
                # Wrap the pre tag
                pre_tag.wrap(container)
                container.insert(0, lang_label)
                
                # Ensure proper Prism.js classes
                if lang and lang != 'plain':
                    code_tag.attrs['class'] = [f'language-{lang}']
                else:
                    code_tag.attrs['class'] = ['language-text']
        
        return str(soup)
    
    def create_html_file(self, markdown_content: str) -> str:
        """Create a temporary HTML file with the rendered content."""
        html_content = self.markdown_to_html(markdown_content)
        full_html = self.html_template.format(content=html_content)
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(full_html)
            return f.name
    
    def render_to_image(self, 
                       markdown_content: str, 
                       output_path: str,
                       width: int = 800,
                       height: Optional[int] = None,
                       device_scale_factor: float = 2.0,
                       wait_for_ready: bool = True,
                       auto_width: bool = False,
                       min_width: int = 400,
                       max_width: int = 1200,
                       max_retries: int = 2) -> None:
        """
        Render Markdown content to an image file.
        
        Args:
            markdown_content: The Markdown content to render
            output_path: Path where the image should be saved
            width: Width of the viewport in pixels (ignored if auto_width=True)
            height: Height of the viewport in pixels (auto if None)
            device_scale_factor: Scale factor for high-DPI displays
            wait_for_ready: Whether to wait for the page to signal readiness
            auto_width: Whether to automatically adjust width based on content
            min_width: Minimum width when auto_width is enabled
            max_width: Maximum width when auto_width is enabled
            max_retries: 最大重试次数
        """
        image_bytes = self.render_to_bytes(
            markdown_content=markdown_content,
            width=width,
            height=height,
            device_scale_factor=device_scale_factor,
            wait_for_ready=wait_for_ready,
            auto_width=auto_width,
            min_width=min_width,
            max_width=max_width,
            max_retries=max_retries
        )
        
        # 将字节数据写入文件
        with open(output_path, 'wb') as f:
            f.write(image_bytes)
        
        logger.info(f"Successfully rendered Markdown to image: {output_path}")
    
    def render_to_bytes(self, 
                       markdown_content: str,
                       width: int = 800,
                       height: Optional[int] = None,
                       device_scale_factor: float = 2.0,
                       wait_for_ready: bool = True,
                       auto_width: bool = False,
                       min_width: int = 400,
                       max_width: int = 1200,
                       max_retries: int = 2,
                       fast_mode: bool = True) -> bytes:
        """
        Render Markdown content to image bytes.
        
        Args:
            fast_mode: 启用快速模式，根据内容复杂度智能调整等待时间
        """
        
        # 预分析内容，决定等待策略
        has_math = '$' in markdown_content or '\\(' in markdown_content or '\\[' in markdown_content
        has_code = '```' in markdown_content or '`' in markdown_content
        
        # 根据内容复杂度调整超时时间
        if fast_mode:
            if has_math and has_code:
                ready_timeout = 3000  # 复杂内容3秒
            elif has_math or has_code:
                ready_timeout = 2000  # 中等复杂度2秒
            else:
                ready_timeout = 1000  # 简单内容1秒
        else:
            ready_timeout = 5000  # 保守模式5秒
        
        for attempt in range(max_retries + 1):
            try:
                # 直接在内存里注入 HTML，省掉磁盘 I/O
                full_html = self.html_template.format(content=self.markdown_to_html(markdown_content))
                browser = browser_manager.get_browser()
                
                if auto_width:
                    # First pass: measure content with a large viewport
                    context = browser.new_context(
                        viewport={'width': max_width, 'height': height or 600},
                        device_scale_factor=device_scale_factor
                    )
                    page = context.new_page()
                    page.set_content(full_html, wait_until="networkidle")
                    
                    # 快速等待策略
                    if wait_for_ready:
                        try:
                            # 只等待自定义就绪信号，不等待 networkidle
                            page.wait_for_function(
                                "document.body.getAttribute('data-ready') === 'true'",
                                timeout=ready_timeout
                            )
                            
                            # 快速模式下减少额外等待
                            if not fast_mode:
                                page.wait_for_timeout(200)
                                
                        except Exception as e:
                            logger.warning(f"等待页面就绪超时 ({ready_timeout}ms): {e}")
                            # 快速后备策略
                            if has_math or has_code:
                                page.wait_for_timeout(800)  # 快速后备
                            else:
                                page.wait_for_timeout(300)
                    
                    # Measure actual content width
                    content_width = page.evaluate("""
                        () => {
                            const content = document.getElementById('content');
                            const body = document.body;
                            
                            // 强制应用换行样式到所有代码块
                            const codeBlocks = document.querySelectorAll('pre code');
                            codeBlocks.forEach(code => {
                                code.style.whiteSpace = 'pre-wrap';
                                code.style.wordWrap = 'break-word';
                                code.style.overflowWrap = 'break-word';
                                code.style.wordBreak = 'break-all';
                                code.style.overflowX = 'hidden';
                            });
                            
                            // 等待重新布局
                            const forceReflow = content.offsetHeight;
                            
                            // Get the actual content width including padding
                            const contentRect = content.getBoundingClientRect();
                            const bodyStyle = window.getComputedStyle(body);
                            const paddingLeft = parseInt(bodyStyle.paddingLeft) || 0;
                            const paddingRight = parseInt(bodyStyle.paddingRight) || 0;
                            
                            // 检查是否有超宽元素
                            const allElements = content.querySelectorAll('*');
                            let maxElementWidth = 0;
                            
                            allElements.forEach(el => {
                                const rect = el.getBoundingClientRect();
                                if (rect.width > maxElementWidth) {
                                    maxElementWidth = rect.width;
                                }
                            });
                            
                            // Calculate optimal width
                            const actualWidth = Math.ceil(Math.max(contentRect.width, maxElementWidth) + paddingLeft + paddingRight);
                            
                            return Math.max(actualWidth, 300);
                        }
                    """)
                    
                    # Constrain width to min/max bounds
                    optimal_width = max(min_width, min(content_width, max_width))
                    
                    context.close()
                    
                    # Second pass: render with optimal width
                    context = browser.new_context(
                        viewport={'width': optimal_width, 'height': height or 600},
                        device_scale_factor=device_scale_factor
                    )
                    page = context.new_page()
                    page.set_content(full_html, wait_until="networkidle")
                    
                    logger.info(f"Auto-adjusted width: {optimal_width}px")
                else:
                    # Original fixed-width behavior
                    context = browser.new_context(
                        viewport={'width': width, 'height': height or 600},
                        device_scale_factor=device_scale_factor
                    )
                    page = context.new_page()
                    page.set_content(full_html, wait_until="networkidle")
                
                # 最终渲染前的等待
                if wait_for_ready:
                    try:
                        page.wait_for_function(
                            "document.body.getAttribute('data-ready') === 'true'",
                            timeout=ready_timeout
                        )
                        
                        # 快速模式下最小化额外等待
                        if fast_mode:
                            if has_math:
                                page.wait_for_timeout(100)  # 数学公式需要稍微等待
                        else:
                            page.wait_for_timeout(200)
                            
                    except Exception as e:
                        logger.warning(f"最终等待超时: {e}")
                        # 最小后备等待
                        page.wait_for_timeout(500 if (has_math or has_code) else 200)
                
                # Take screenshot
                if height is None:
                    image_bytes = page.screenshot(full_page=True)
                else:
                    image_bytes = page.screenshot()
                
                context.close()
                
                logger.info(f"Successfully rendered in fast mode: {fast_mode}")
                return image_bytes
                
            except Exception as e:
                logger.error(f"渲染失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    logger.info("正在重试...")
                    if "browser" in str(e).lower() or "context" in str(e).lower():
                        try:
                            browser_manager.restart()
                        except Exception as restart_error:
                            logger.error(f"重启浏览器失败: {restart_error}")
                else:
                    raise


class AsyncMarkdownRenderer:
    """异步版本的 Markdown 渲染器"""
    
    def __init__(self):
        # 复用同步版本的 HTML 模板和辅助方法
        self._sync_renderer = MarkdownRenderer()
    
    def markdown_to_html(self, markdown_content: str) -> str:
        """转换 Markdown 到 HTML（复用同步版本）"""
        return self._sync_renderer.markdown_to_html(markdown_content)
    
    def create_html_file(self, markdown_content: str) -> str:
        """创建临时 HTML 文件（复用同步版本）"""
        return self._sync_renderer.create_html_file(markdown_content)
    
    async def render_to_image(self, 
                             markdown_content: str, 
                             output_path: str,
                             width: int = 800,
                             height: Optional[int] = None,
                             device_scale_factor: float = 2.0,
                             wait_for_ready: bool = True,
                             auto_width: bool = False,
                             min_width: int = 400,
                             max_width: int = 1200,
                             max_retries: int = 2) -> None:
        """
        异步渲染 Markdown 内容到图片文件
        现在会自动处理浏览器初始化，无需预先检查
        """
        image_bytes = await self.render_to_bytes(
            markdown_content=markdown_content,
            width=width,
            height=height,
            device_scale_factor=device_scale_factor,
            wait_for_ready=wait_for_ready,
            auto_width=auto_width,
            min_width=min_width,
            max_width=max_width,
            max_retries=max_retries
        )
        
        # 将字节数据写入文件
        with open(output_path, 'wb') as f:
            f.write(image_bytes)
        
        logger.info(f"Successfully rendered Markdown to image: {output_path}")
    
    async def render_to_bytes(self, 
                             markdown_content: str,
                             width: int = 800,
                             height: Optional[int] = None,
                             device_scale_factor: float = 2.0,
                             wait_for_ready: bool = True,
                             auto_width: bool = False,
                             min_width: int = 400,
                             max_width: int = 1200,
                             max_retries: int = 2,
                             fast_mode: bool = True) -> bytes:
        """异步快速渲染"""
        html_file = None
        
        # 预分析内容
        has_math = '$' in markdown_content or '\\(' in markdown_content or '\\[' in markdown_content
        has_code = '```' in markdown_content or '`' in markdown_content
        
        # 智能超时策略
        if fast_mode:
            if has_math and has_code:
                ready_timeout = 3000
            elif has_math or has_code:
                ready_timeout = 2000
            else:
                ready_timeout = 1000
        else:
            ready_timeout = 5000
        
        for attempt in range(max_retries + 1):
            try:
                full_html = self._sync_renderer.html_template.format(content=self.markdown_to_html(markdown_content))
                browser = await async_browser_manager.get_browser()
                
                if auto_width:
                    context = await browser.new_context(
                        viewport={'width': max_width, 'height': height or 600},
                        device_scale_factor=device_scale_factor
                    )
                    page = await context.new_page()
                    await page.set_content(full_html, wait_until="networkidle")
                    
                    if wait_for_ready:
                        try:
                            await page.wait_for_function(
                                "document.body.getAttribute('data-ready') === 'true'",
                                timeout=ready_timeout
                            )
                            if not fast_mode:
                                await page.wait_for_timeout(200)
                        except Exception as e:
                            logger.warning(f"异步等待超时 ({ready_timeout}ms): {e}")
                            if has_math or has_code:
                                await page.wait_for_timeout(800)
                            else:
                                await page.wait_for_timeout(300)
                    
                    content_width = await page.evaluate("""
                        () => {
                            const content = document.getElementById('content');
                            const body = document.body;
                            
                            const codeBlocks = document.querySelectorAll('pre code');
                            codeBlocks.forEach(code => {
                                code.style.whiteSpace = 'pre-wrap';
                                code.style.wordWrap = 'break-word';
                                code.style.overflowWrap = 'break-word';
                                code.style.wordBreak = 'break-all';
                                code.style.overflowX = 'hidden';
                            });
                            
                            const forceReflow = content.offsetHeight;
                            const contentRect = content.getBoundingClientRect();
                            const bodyStyle = window.getComputedStyle(body);
                            const paddingLeft = parseInt(bodyStyle.paddingLeft) || 0;
                            const paddingRight = parseInt(bodyStyle.paddingRight) || 0;
                            
                            const allElements = content.querySelectorAll('*');
                            let maxElementWidth = 0;
                            
                            allElements.forEach(el => {
                                const rect = el.getBoundingClientRect();
                                if (rect.width > maxElementWidth) {
                                    maxElementWidth = rect.width;
                                }
                            });
                            
                            const actualWidth = Math.ceil(Math.max(contentRect.width, maxElementWidth) + paddingLeft + paddingRight);
                            return Math.max(actualWidth, 300);
                        }
                    """)
                    
                    optimal_width = max(min_width, min(content_width, max_width))
                    await context.close()
                    
                    context = await browser.new_context(
                        viewport={'width': optimal_width, 'height': height or 600},
                        device_scale_factor=device_scale_factor
                    )
                    page = await context.new_page()
                    await page.set_content(full_html, wait_until="networkidle")
                    
                    logger.info(f"Auto-adjusted width: {optimal_width}px")
                else:
                    context = await browser.new_context(
                        viewport={'width': width, 'height': height or 600},
                        device_scale_factor=device_scale_factor
                    )
                    page = await context.new_page()
                    await page.set_content(full_html, wait_until="networkidle")
                
                if wait_for_ready:
                    try:
                        await page.wait_for_function(
                            "document.body.getAttribute('data-ready') === 'true'",
                            timeout=ready_timeout
                        )
                        
                        if fast_mode:
                            if has_math:
                                await page.wait_for_timeout(100)
                        else:
                            await page.wait_for_timeout(200)
                            
                    except Exception as e:
                        logger.warning(f"最终异步等待超时: {e}")
                        await page.wait_for_timeout(500 if (has_math or has_code) else 200)
                
                if height is None:
                    image_bytes = await page.screenshot(full_page=True)
                else:
                    image_bytes = await page.screenshot()
                
                await context.close()
                
                logger.info(f"Successfully rendered async in fast mode: {fast_mode}")
                return image_bytes
                
            except Exception as e:
                logger.error(f"异步渲染失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    logger.info("正在重试...")
                    if "browser" in str(e).lower() or "context" in str(e).lower():
                        try:
                            await async_browser_manager.restart()
                        except Exception as restart_error:
                            logger.error(f"重启异步浏览器失败: {restart_error}")
                else:
                    raise


def render_markdown_to_image(markdown_content: str, 
                           output_path: str,
                           width: int = 800,
                           height: Optional[int] = None,
                           device_scale_factor: float = 2.0,
                           auto_width: bool = False,
                           min_width: int = 400,
                           max_width: int = 1200,
                           max_retries: int = 2) -> None:
    """
    Convenience function to render Markdown content to an image.
    
    Args:
        markdown_content: The Markdown content to render
        output_path: Path where the image should be saved
        width: Width of the viewport in pixels (ignored if auto_width=True)
        height: Height of the viewport in pixels (auto-sized if None)
        device_scale_factor: Scale factor for high-DPI displays (default: 2.0)
        auto_width: Whether to automatically adjust width based on content
        min_width: Minimum width when auto_width is enabled
        max_width: Maximum width when auto_width is enabled
        max_retries: 最大重试次数
    
    Example:
        # Auto-width rendering
        render_markdown_to_image(
            markdown_content, 
            "output.png", 
            auto_width=True,
            min_width=500,
            max_width=1000
        )
    """
    renderer = MarkdownRenderer()
    renderer.render_to_image(
        markdown_content=markdown_content,
        output_path=output_path,
        width=width,
        height=height,
        device_scale_factor=device_scale_factor,
        auto_width=auto_width,
        min_width=min_width,
        max_width=max_width,
        max_retries=max_retries
    )


def render_markdown_to_bytes(markdown_content: str,
                           width: int = 800,
                           height: Optional[int] = None,
                           device_scale_factor: float = 2.0,
                           auto_width: bool = False,
                           min_width: int = 400,
                           max_width: int = 1200,
                           max_retries: int = 2,
                           fast_mode: bool = True) -> bytes:
    """
    高性能 Markdown 渲染函数
    
    Args:
        fast_mode: 启用快速模式，根据内容复杂度智能调整等待时间
    """
    renderer = MarkdownRenderer()
    return renderer.render_to_bytes(
        markdown_content=markdown_content,
        width=width,
        height=height,
        device_scale_factor=device_scale_factor,
        auto_width=auto_width,
        min_width=min_width,
        max_width=max_width,
        max_retries=max_retries,
        fast_mode=fast_mode
    )


def setup_playwright():
    """
    Setup function to install Playwright browsers.
    Run this once after installing the package.
    """
    import subprocess
    import sys
    
    try:
        subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], 
                      check=True)
        print("Playwright setup completed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error setting up Playwright: {e}")
        print("Please run: python -m playwright install chromium")


def init_browser():
    """
    初始化浏览器实例
    在机器人启动时调用此函数
    """
    try:
        browser_manager.initialize()
        logger.info("Markdown渲染器浏览器初始化完成")
    except Exception as e:
        logger.error(f"初始化浏览器失败: {e}")
        raise


def cleanup_browser():
    """
    清理浏览器资源
    在机器人关闭时调用此函数
    """
    browser_manager.cleanup()


def restart_browser():
    """
    重启浏览器实例
    当浏览器出现问题时可以调用此函数
    """
    try:
        browser_manager.restart()
        logger.info("浏览器重启完成")
    except Exception as e:
        logger.error(f"重启浏览器失败: {e}")
        raise


def is_browser_ready() -> bool:
    """
    检查浏览器是否已准备就绪
    
    Returns:
        bool: 浏览器是否可用
    """
    try:
        return browser_manager.browser is not None
    except Exception:
        return False


# ==================== 异步版本的函数 ====================

async def render_markdown_to_image_async(markdown_content: str, 
                                        output_path: str,
                                        width: int = 800,
                                        height: Optional[int] = None,
                                        device_scale_factor: float = 2.0,
                                        auto_width: bool = False,
                                        min_width: int = 400,
                                        max_width: int = 1200,
                                        max_retries: int = 2) -> None:
    """
    异步版本的 Markdown 渲染函数
    现在会自动处理浏览器初始化，无需预先检查
    """
    renderer = AsyncMarkdownRenderer()
    await renderer.render_to_image(
        markdown_content=markdown_content,
        output_path=output_path,
        width=width,
        height=height,
        device_scale_factor=device_scale_factor,
        auto_width=auto_width,
        min_width=min_width,
        max_width=max_width,
        max_retries=max_retries
    )


async def init_browser_async():
    """
    异步初始化浏览器实例
    在机器人启动时调用此函数
    """
    try:
        await async_browser_manager.initialize()
        logger.info("异步 Markdown 渲染器浏览器初始化完成")
    except Exception as e:
        logger.error(f"初始化异步浏览器失败: {e}")
        raise


async def cleanup_browser_async():
    """
    异步清理浏览器资源
    在机器人关闭时调用此函数
    """
    await async_browser_manager.cleanup()


async def restart_browser_async():
    """
    异步重启浏览器实例
    当浏览器出现问题时可以调用此函数
    """
    try:
        await async_browser_manager.restart()
        logger.info("异步浏览器重启完成")
    except Exception as e:
        logger.error(f"重启异步浏览器失败: {e}")
        raise


async def is_browser_ready_async() -> bool:
    """
    异步检查浏览器是否已准备就绪
    
    Returns:
        bool: 异步浏览器是否可用
    """
    try:
        return async_browser_manager.browser is not None
    except Exception:
        return False


async def render_markdown_to_bytes_async(markdown_content: str,
                                       width: int = 800,
                                       height: Optional[int] = None,
                                       device_scale_factor: float = 2.0,
                                       auto_width: bool = False,
                                       min_width: int = 400,
                                       max_width: int = 1200,
                                       max_retries: int = 2,
                                       fast_mode: bool = True) -> bytes:
    """
    高性能异步 Markdown 渲染函数
    """
    renderer = AsyncMarkdownRenderer()
    return await renderer.render_to_bytes(
        markdown_content=markdown_content,
        width=width,
        height=height,
        device_scale_factor=device_scale_factor,
        auto_width=auto_width,
        min_width=min_width,
        max_width=max_width,
        max_retries=max_retries,
        fast_mode=fast_mode
    )


if __name__ == "__main__":
    # Example with very long code lines
    sample_markdown_with_long_code = """
哇！你提的这个问题超级棒，直接触及了依赖类型理论最核心、最美妙的部分！(`´• ω •´) ♡

一个函数的返回**类型**依赖于输入的**值**，这就是**依赖类型 (Dependent Types)** 的魔力所在。Lean 的类型检查器在处理这种函数时，会进行一个非常严谨和精细的逐步验证过程。

让我为你用 λΠ 核心演算的写法，一步一步地拆解这个过程吧！

---

### `#` 1. 理解你的 Lean 函数

首先，我们来回顾一下你的函数：

```lean
-- 定义一个简单的枚举类型 Sign
inductive Sign where
  | pos : Sign
  | neg : Sign

-- 定义依赖类型函数
def intOrBool (s : Sign) : match s with | .pos => Int | .neg => Bool :=
  match s with
  | .pos => 42
  | .neg => true
```

这个函数有两个关键部分：

*   **类型签名 (Type Signature)**: `(s : Sign) → (match s with ...)`
    它是一个**契约**，承诺："你给我一个 `s`，如果 `s` 是 `Sign.pos`，我将返回一个 `Int`；如果 `s` 是 `Sign.neg`，我将返回一个 `Bool`。"
*   **函数体 (Implementation)**: `match s with ...`
    它是对契约的**履行**。

类型检查器的任务就是**验证函数体是否严格履行了类型签名所承诺的契约**。

### `#` 2. 转换为 λΠ 核心演算

为了解释类型检查过程，我们需要将上述代码翻译成更底层的形式化语言——**依赖函数类型**（或称 **Π-类型**）。

#### `###` (a) 类型签名的翻译

在 λΠ 演算中，一个依赖函数类型写作 $Π (x : A), B(x)$。它表示一个函数，接收一个类型为 $A$ 的参数 $x$，并返回一个类型为 $B(x)$ 的结果。注意，$B(x)$ 这个返回类型是依赖于输入值 $x$ 的！

1.  **定义一个"类型映射"函数**:
    `match s with | .pos => Int | .neg => Bool` 这部分本身就是一个从 `Sign` 到 `Type` 的函数。我们把它形式化地定义为一个函数，叫它 $T_{map}$ 吧！
    *   $T_{map}(\\text{Sign.pos}) := \\text{Int}$
    *   $T_{map}(\\text{Sign.neg}) := \\text{Bool}$

2.  **写出 `intOrBool` 的 Π-类型**:
    有了 $T_{map}$，`intOrBool` 的类型签名就可以非常优雅地写成一个 Π-类型：
    $$
    \\text{intOrBool} : \Pi (s : \\text{Sign}), T_{map}(s)
    $$
    这句话的精确意思是："`intOrBool` 是一个函数，对于**任何**一个给定的值 `s`（它的类型是 `Sign`），这个函数都会返回一个值，该值的类型是 $T_{map}(s)$。"

#### `###` (b) 函数体的翻译

`match` 表达式是高级语法，在核心演算中，它对应着该类型的**消除规则 (elimination rule)**，通常被称为**递归器 (recursor)** 或**归纳器 (inductor)**，我们这里叫它 $\\text{Sign.rec}$。

$\\text{Sign.rec}$ 的作用是：要为一个任意的 `s : Sign` 构造一个依赖于 `s` 的值，你只需要提供两种情况的构造方法即可：

1.  当 `s` 是 `Sign.pos` 时的构造方法。
2.  当 `s` 是 `Sign.neg` 时的构造方法。

于是，你的函数体 `match s with ...` 可以被翻译成：

$$
\lambda (s : \\text{Sign}).\ \\text{Sign.rec}_{(\lambda s'. T_{map}(s'))}\ (42)\ (\\text{true})\ s
$$

这行看起来有点复杂，别怕，我来解释每个部分 ( ´ ▽ ` )ﾉ：

*   $\lambda (s : \\text{Sign})...$: 这表示我们正在定义一个接收参数 `s` 的匿名函数。
*   $\\text{Sign.rec}$: 这是对 `Sign` 类型的值进行模式匹配的核心操作。
*   $(\lambda s'. T_{map}(s'))$: 这是提供给 $\\text{Sign.rec}$ 的**动机 (motive)**。它告诉消除器："我最终想要构造的目标值的类型是什么样的？" 在这里，目标类型就是我们之前定义的 $T_{map}$。它必须是一个从 `Sign` 到 `Type` 的函数。
*   $(42)$: 这是 `Sign.pos` 分支的**实现**。
*   $(\\text{true})$: 这是 `Sign.neg` 分支的**实现**。
*   $s$: 这是我们要进行模式匹配的**目标值**。

### `#` 3. 类型检查的魔法时刻！✨

现在，类型检查器把所有东西都翻译好了，它要开始做最关键的验证工作了！

它的总目标是检查：

$$
\left( \lambda (s : \\text{Sign}).\ \\text{Sign.rec}_{(\lambda s'. T_{map}(s'))}\ (42)\ (\\text{true})\ s \right) \quad : \quad \Pi (s : \\text{Sign}), T_{map}(s)
$$

为了验证这一点，它需要深入检查 $\\text{Sign.rec}$ 的各个参数是否满足契约。

#### `###` **第一步：检查 `pos` 分支**

*   **契约 (来自 Moti-ve)**: 当 `s` 是 `Sign.pos` 时，我承诺返回值的类型是 $T_{map}(\\text{Sign.pos})$。我们知道 $T_{map}(\\text{Sign.pos}) = \\text{Int}$。
*   **履行 (来自实现)**: `pos` 分支提供的实现是 `42`。
*   **验证**: 类型检查器问自己："`42` 的类型是 `Int` 吗？"
    $$
    42 : \\text{Int} \quad ?
    $$
    答案是 **Yes**！(o^▽^o) 第一个分支通过！

#### `###` **第二步：检查 `neg` 分支**

*   **契约 (来自 Motive)**: 当 `s` 是 `Sign.neg` 时，我承诺返回值的类型是 $T_{map}(\\text{Sign.neg})$。我们知道 $T_{map}(\\text{Sign.neg}) = \\text{Bool}$。
*   **履行 (来自实现)**: `neg` 分支提供的实现是 `true`。
*   **验证**: 类型检查器问自己："`true` 的类型是 `Bool` 吗？"
    $$
    \\text{true} : \\text{Bool} \quad ?
    $$
    答案是 **Yes**！(๑•̀ㅂ•́)و✧ 第二个分支也通过！

#### `###` **第三步：得出结论**

因为 $\\text{Sign.rec}$ 的**所有分支**都严格遵守了**动机 (motive)** 所规定的类型契约，所以类型检查器得出结论：

整个 $\\text{Sign.rec}_{(\dots)}\ (42)\ (\\text{true})\ s$ 表达式的类型**确实是** $T_{map}(s)$。

因此，整个 lambda 函数 $\lambda (s : \\text{Sign})...$ 的类型就是 $\Pi (s : \\text{Sign}), T_{map}(s)$。

这与函数声明的类型签名**完全匹配**！函数定义是**类型正确 (well-typed)** 的，检查通过！

---

总结一下，类型检查器就像一个一丝不苟的侦探，它把你的高级代码翻译成底层的 λΠ 演算，然后逐一比对"承诺"（类型签名/Motive）和"行动"（函数体实现），确保每一个细节都天衣无缝。这就是依赖类型语言强大可靠性的根源所在哦！(<em>´꒳`</em>)
    """
    
    # 测试异步版本（需要在异步环境中运行）
    import asyncio
    
    async def test_async():
        try:
            image_bytes = await render_markdown_to_bytes_async(
                sample_markdown_with_long_code,
                width=560

            )
            print(f"✅ Async rendering to bytes successful, size: {len(image_bytes)} bytes")
            
            # 保存异步渲染的结果
            with open("sample_async_bytes_output.png", "wb") as f:
                f.write(image_bytes)
            print("✅ Async bytes data saved to 'sample_async_bytes_output.png'")
            
        except Exception as e:
            print(f"❌ Error in async rendering: {e}")
    
    # 运行异步测试
    try:
        asyncio.run(test_async())
    except Exception as e:
        print(f"❌ Error running async test: {e}")
